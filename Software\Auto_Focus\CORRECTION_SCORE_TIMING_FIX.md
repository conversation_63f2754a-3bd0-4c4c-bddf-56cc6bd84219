# Perbaikan Timing Skor Koreksi AF

## Masalah yang Ditemukan

### **Skor Koreksi Tidak Akurat**
```
Hasil Manual vs Mapping di titik yang sama:
- Manual di Z=10.075: Skor = 29.xx (akurat)
- Mapping di Z=10.075: Skor = 23.84 (tidak akurat)

Perbedaan: ~5-6 poin skor (sangat signifikan)
```

### **<PERSON><PERSON><PERSON>**

#### **1. Stabilisasi Terlalu <PERSON>kat**
```python
# Kode sebelumnya:
self.grbl.move_to_z(corrected_z)
self.grbl.wait_for_idle()
time.sleep(0.2)  # Hanya 0.2 detik!

# Masalah:
# - GRBL mungkin belum benar-benar stabil
# - Kamera belum sempat menyesuaikan
# - Frame masih blur dari gerakan
```

#### **2. Single Frame Sampling**
```python
# Kode sebelumnya:
frame = self.camera.get_latest_numpy_frame()
score = self._calculate_focus_score(frame)

# Masalah:
# - Hanya 1 frame untuk menentukan skor
# - <PERSON>tan terhadap noise/fluktuasi sesaat
# - Tidak representatif untuk kondisi stabil
```

#### **3. Inconsistent Sampling Method**
```python
# Verifikasi: Single frame
# Koreksi: Single frame
# Tapi timing berbeda, kondisi berbeda
```

## Solusi yang Diterapkan

### **1. Extended Stabilization**

#### **Sebelum (Tidak Akurat):**
```python
self.grbl.move_to_z(corrected_z)
self.grbl.wait_for_idle()
time.sleep(0.2)  # Terlalu singkat
```

#### **Sesudah (Akurat):**
```python
print("Menunggu mesin GRBL menjadi 'Idle'...")
self.grbl.move_to_z(corrected_z)
self.grbl.wait_for_idle()
print("Mesin sudah 'Idle'. Melanjutkan.")

print("  -> Stabilisasi posisi koreksi...")
time.sleep(1.0)  # 5x lebih lama (1.0 vs 0.2 detik)
```

### **2. Multiple Frame Sampling**

#### **Sebelum (Single Frame):**
```python
correction_frame = self.camera.get_latest_numpy_frame()
if correction_frame is not None:
    correction_score, _ = self._calculate_focus_score(correction_frame)
```

#### **Sesudah (Multiple Frames):**
```python
print("  -> Mengambil multiple frames untuk skor koreksi...")
correction_scores = []
for i in range(5):  # Ambil 5 frame
    correction_frame = self.camera.get_latest_numpy_frame()
    if correction_frame is not None:
        score, _ = self._calculate_focus_score(correction_frame)
        correction_scores.append(score)
        print(f"     Frame {i+1}: {score:.2f}")
    time.sleep(0.1)  # Delay antar frame

# Gunakan rata-rata dari multiple frames
if correction_scores:
    correction_score = sum(correction_scores) / len(correction_scores)
    print(f"  -> Skor Fokus Koreksi: {correction_score:.2f} (rata-rata dari {len(correction_scores)} frames)")
```

### **3. Consistent Sampling untuk Verifikasi**

#### **Verifikasi juga menggunakan Multiple Frame Sampling:**
```python
print("  -> Mengambil multiple frames untuk verifikasi...")
verification_scores = []
for i in range(5):  # Ambil 5 frame
    verification_frame = self.camera.get_latest_numpy_frame()
    if verification_frame is not None:
        score, _ = self._calculate_focus_score(verification_frame)
        verification_scores.append(score)
        print(f"     Frame {i+1}: {score:.2f}")
    time.sleep(0.1)  # Delay antar frame

# Gunakan rata-rata
if verification_scores:
    verify_score = sum(verification_scores) / len(verification_scores)
    print(f"  -> Skor Fokus Verifikasi: {verify_score:.2f} (rata-rata dari {len(verification_scores)} frames)")
```

### **4. Enhanced Movement Feedback**

#### **Verbose Logging untuk Debugging:**
```python
print("Menunggu mesin GRBL menjadi 'Idle'...")
self.grbl.move_to_z(corrected_z)
self.grbl.wait_for_idle()
print("Mesin sudah 'Idle'. Melanjutkan.")
```

## Hasil yang Diharapkan

### **Sebelum Perbaikan:**
```
[AF COMPARISON] Membandingkan Skor Verifikasi vs Koreksi...
  -> Skor Fokus Verifikasi: 27.26 (di Z=10.0958)
  -> Skor Fokus Koreksi   : 23.84 (di Z=10.0748)  ← TIDAK AKURAT
  -> KEPUTUSAN: Menggunakan Z VERIFIKASI = 10.0958 (Skor lebih tinggi: 27.26)
```

### **Sesudah Perbaikan:**
```
[AF COMPARISON] Membandingkan Skor Verifikasi vs Koreksi...
  -> Skor Fokus Verifikasi: 27.26 (di Z=10.0958)
  -> Skor Fokus Koreksi   : 29.14 (di Z=10.0748)  ← AKURAT
  -> KEPUTUSAN: Menggunakan Z KOREKSI = 10.0748 (Skor lebih tinggi: 29.14)
```

### **Konsistensi dengan Manual:**
```
Manual Test di Z=10.075: Skor = 29.xx
Mapping di Z=10.075: Skor = 29.14 (rata-rata 5 frames)
Perbedaan: < 1 poin (dapat diterima)
```

## Log Output Detail

### **Extended Stabilization Log:**
```
[AF CORRECTION] Memulai Koreksi Gerakan Akhir...
  -> Arah Tahap 2         : Naik
  -> Posisi Z Awal        : 10.0958
  -> Offset Koreksi       : -0.0210 mm
  -> Posisi Z Terkoreksi  : 10.0748
Menunggu mesin GRBL menjadi 'Idle'...
Mesin sudah 'Idle'. Melanjutkan.
  -> Stabilisasi posisi koreksi...
```

### **Multiple Frame Sampling Log:**
```
  -> Mengambil multiple frames untuk skor koreksi...
     Frame 1: 29.12
     Frame 2: 28.95
     Frame 3: 29.34
     Frame 4: 29.08
     Frame 5: 29.21
  -> Skor Fokus Koreksi   : 29.14 (rata-rata dari 5 frames)
     (Skor Terbaik: 29.48, Skor Frame Akhir: 27.26)
```

### **Accurate Comparison Log:**
```
[AF COMPARISON] Membandingkan Skor Verifikasi vs Koreksi...
  -> Skor Fokus Verifikasi: 27.26 (di Z=10.0958)
  -> Skor Fokus Koreksi   : 29.14 (di Z=10.0748)
  -> KEPUTUSAN: Menggunakan Z KOREKSI = 10.0748 (Skor lebih tinggi: 29.14)
```

## Manfaat Perbaikan

### **1. Akurasi Tinggi**
- Skor koreksi sekarang konsisten dengan test manual
- Perbedaan < 1 poin vs manual test
- Multiple frame sampling mengurangi noise

### **2. Stabilitas**
- Extended stabilization memastikan kondisi benar-benar stabil
- Consistent sampling method untuk verifikasi dan koreksi
- Verbose logging untuk debugging

### **3. Reliability**
- Keputusan pemilihan Z berdasarkan data yang akurat
- Mengurangi false negative (koreksi sebenarnya lebih baik tapi terdeteksi buruk)
- Hasil mapping lebih konsisten dengan expectation

### **4. Debugging Capability**
- Log detail untuk setiap frame
- Verbose movement feedback
- Clear indication of sampling method

## Parameter yang Dapat Disesuaikan

### **Jumlah Frame Sampling:**
```python
# Saat ini: 5 frames
for i in range(5):  # Bisa diubah ke 3, 7, atau 10

# Trade-off:
# - Lebih banyak frame = lebih akurat tapi lebih lambat
# - Lebih sedikit frame = lebih cepat tapi kurang stabil
```

### **Stabilization Time:**
```python
# Saat ini: 1.0 detik
time.sleep(1.0)  # Bisa diubah ke 0.5, 1.5, atau 2.0

# Trade-off:
# - Lebih lama = lebih stabil tapi mapping lebih lambat
# - Lebih singkat = lebih cepat tapi mungkin kurang akurat
```

### **Inter-frame Delay:**
```python
# Saat ini: 0.1 detik antar frame
time.sleep(0.1)  # Bisa diubah ke 0.05 atau 0.2

# Trade-off:
# - Lebih lama = frame lebih independent tapi sampling lebih lambat
# - Lebih singkat = sampling lebih cepat tapi frame mungkin masih berkorelasi
```

## Testing dan Validasi

### **1. Consistency Test:**
```
1. Jalankan mapping AF di area yang sama 3x
2. Bandingkan skor koreksi untuk titik yang sama
3. Variasi skor < 1 poin = konsisten
```

### **2. Manual Validation:**
```
1. Catat Z koreksi dari mapping
2. Test manual di Z yang sama
3. Perbedaan skor < 1 poin = akurat
```

### **3. Performance Impact:**
```
1. Ukur waktu tambahan per titik
2. Pastikan tidak ada timeout
3. Monitor stabilitas GRBL dan kamera
```
