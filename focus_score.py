import cv2
import numpy as np
import os
import tkinter as tk
from tkinter import filedialog
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Pattern<PERSON>ill

def sobel_focus_score(frame: np.ndarray) -> float:
        """
        Menghitung focus score menggunakan Sobel gradient magnitude
        Parameter optimal: resize_factor=0.2, ksize=1, weights=(0.5, 0.5)
        Threshold range: 0-50, good_threshold=15
        """
        # Parameter optimal untuk method ini
        resize_factor = 0.2
        sobel_ksize = 1
        weight_gx = 0.5
        weight_gy = 0.5

        # Convert ke grayscale
        img = cv2.imread(frame)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Resize untuk optimasi
        small_gray = cv2.resize(gray, (0, 0), fx=resize_factor, fy=resize_factor)

        # Hitung gradient menggunakan Sobel
        gx = cv2.Sobel(small_gray, cv2.CV_16S, 1, 0, ksize=sobel_ksize)
        gy = cv2.Sobel(small_gray, cv2.CV_16S, 0, 1, ksize=sobel_ksize)

        # Convert ke absolute values
        abs_gx = cv2.convertScaleAbs(gx)
        abs_gy = cv2.convertScaleAbs(gy)

        # Combine gradients dengan weighted average
        gradient_magnitude = cv2.addWeighted(abs_gx, weight_gx, abs_gy, weight_gy, 0)

        # Return mean sebagai focus score
        return float(gradient_magnitude.mean())

# === PILIH FOLDER ===
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Pilih Folder Gambar")

if not folder_path:
    print("Tidak ada folder dipilih.")
    exit()

# === BACA FILE DALAM FOLDER ===
files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.png','.jpg','.jpeg','.bmp','.tif','.tiff'))]

fixed_files = [f for f in files if "fixed" in f.lower()]
interp_files = [f for f in files if "interp" in f.lower()]

results = []

# Bandingkan per pasangan berdasarkan p###
for fixed_file in fixed_files:
    parts = fixed_file.split("_")
    p_code = next((p for p in parts if p.startswith("p") and len(p) == 4), None)
    if not p_code:
        continue
    
    interp_match = next((f for f in interp_files if p_code in f), None)
    if interp_match:
        fixed_path = os.path.join(folder_path, fixed_file)
        interp_path = os.path.join(folder_path, interp_match)
        
        fixed_score = sobel_focus_score(fixed_path)
        interp_score = sobel_focus_score(interp_path)
        
        if fixed_score is None or interp_score is None:
            continue
        
        results.append({
            "Nama": p_code,
            "Fixed Score": fixed_score,
            "Interp Score": interp_score
        })

df = pd.DataFrame(results).sort_values("Nama")

if df.empty:
    print("=== TIDAK ADA DATA UNTUK DIANALISIS ===")
    exit()

# === Buat Excel ===
wb = Workbook()

# Warna
green_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")  # Interp menang
red_fill = PatternFill(start_color="FF7F7F", end_color="FF7F7F", fill_type="solid")    # Fixed menang
yellow_fill = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid") # Seri

# === Sheet 1: Raw Data ===
ws_raw = wb.active
ws_raw.title = "Raw Data"
ws_raw.append(["Nama", "Fixed Score", "Interp Score"])

for row in df.itertuples(index=False):
    ws_raw.append([row.Nama, round(row._1, 2), round(row._2, 2)])

# === Sheet 2: Grid (8x8) ===
ws_grid = wb.create_sheet(title="Grid Winner")

for idx, row in enumerate(df.itertuples(index=False), start=0):
    r = idx // 7 + 1  # row dalam grid
    c = idx % 6 + 1   # col dalam grid
    
    val_fixed = round(row._1, 2)
    val_interp = round(row._2, 2)
    
    # Pilih pemenang
    if val_fixed > val_interp:
        cell = ws_grid.cell(row=r, column=c, value=val_fixed)
        cell.fill = red_fill
    elif val_interp > val_fixed:
        cell = ws_grid.cell(row=r, column=c, value=val_interp)
        cell.fill = green_fill
    else:
        cell = ws_grid.cell(row=r, column=c, value=val_fixed)  # sama aja
        cell.fill = yellow_fill

# hapus default sheet kalau masih ada
if "Sheet" in wb.sheetnames:
    wb.remove(wb["Sheet"])

# Simpan file
output_path = os.path.join(folder_path, "laporan_focus_grid.xlsx")
wb.save(output_path)

# Analisis akhir
fixed_win = (df["Fixed Score"] > df["Interp Score"]).sum()
interp_win = (df["Fixed Score"] < df["Interp Score"]).sum()
avg_fixed = df["Fixed Score"].mean()
avg_interp = df["Interp Score"].mean()

print("=== HASIL ANALISIS ===")
print(f"Total pasangan dibandingkan : {len(df)}")
print(f"Fixed lebih tajam   : {fixed_win} kali")
print(f"Interp lebih tajam  : {interp_win} kali")
print(f"Rata-rata skor Fixed   : {avg_fixed:.2f}")
print(f"Rata-rata skor Interp  : {avg_interp:.2f}")
print(f"Laporan grid disimpan di: {output_path}")
