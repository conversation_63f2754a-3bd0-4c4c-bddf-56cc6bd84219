# Mapping AF dengan Perbandingan Skor Verifikasi vs Koreksi

## Deskripsi Fitur

Fitur ini menambahkan kemampuan untuk membandingkan skor fokus antara:
1. **Skor Verifikasi**: Skor fokus pada posisi Z hasil sapuan asli
2. **Skor Koreksi**: Skor fokus pada posisi Z setelah diterapkan koreksi offset (±0.021mm)

Sistem akan secara otomatis memilih posisi Z dengan skor fokus yang lebih tinggi.

## Cara Kerja

### 1. Proses AF Normal
- Tahap 1: Coarse scan untuk mencari area fokus
- Tahap 2: Fine scan untuk mendapatkan posisi Z optimal
- Verifikasi: Mengukur skor fokus di posisi Z hasil sapuan

### 2. Koreksi AF (Baru)
- Menentukan arah sapuan tahap 2 (naik/turun)
- Menerapkan offset koreksi:
  - Jika arah naik: offset = -0.021mm
  - Jika arah turun: offset = +0.021mm
- Mengu<PERSON>r skor fokus di posisi Z terkoreksi

### 3. Perbandingan dan <PERSON>em<PERSON>han
- Membandingkan skor verifikasi vs skor koreksi
- Memilih posisi Z dengan skor fokus lebih tinggi
- Menyimpan hasil pilihan untuk digunakan di Mapping AF

## Output Log Contoh

```
[AF CORRECTION] Memulai Koreksi Gerakan Akhir...
  -> Arah Tahap 2         : Naik
  -> Posisi Z Awal        : 10.1044
  -> Offset Koreksi       : -0.0210 mm
  -> Posisi Z Terkoreksi  : 10.0834
Menunggu mesin GRBL menjadi 'Idle'...
Mesin sudah 'Idle'. Melanjutkan.
  -> Skor Fokus Koreksi   : 28.86
     (Skor Terbaik: 29.48, Skor Frame Akhir: 25.20)

[AF COMPARISON] Membandingkan Skor Verifikasi vs Koreksi...
  -> Skor Fokus Verifikasi: 25.20 (di Z=10.1044)
  -> Skor Fokus Koreksi   : 28.86 (di Z=10.0834)
  -> KEPUTUSAN: Menggunakan Z KOREKSI = 10.0834 (Skor lebih tinggi: 28.86)

[SCORE COMPARISON] Point (32.30, 3.63):
  -> Verifikasi: Z=10.1044, Score=25.20
  -> Koreksi: Z=10.0834, Score=28.86
  -> Dipilih: Z=10.0834, Score=28.86
  -> Menggunakan: KOREKSI
```

## Penggunaan di Mapping AF

### 1. Hasil Z Optimized
```python
# Setelah mapping selesai
mapping_worker = MappingAFWorker(camera, grbl, start, end)
mapping_worker.run()

# Dapatkan hasil Z yang sudah dioptimalkan
optimized_results = mapping_worker.get_optimized_results()
# optimized_results berisi Z yang dipilih berdasarkan skor terbaik

# Bandingkan dengan hasil asli
original_results = mapping_worker.results
# original_results berisi Z hasil sapuan asli (sebelum koreksi)
```

### 2. Summary Perbandingan Skor
```python
# Dapatkan ringkasan perbandingan
summary = mapping_worker.get_score_comparison_summary()

if summary:
    print(f"Total points: {summary['total_points']}")
    print(f"Menggunakan koreksi: {summary['correction_used']}")
    print(f"Menggunakan verifikasi: {summary['verification_used']}")
    
    for detail in summary['details']:
        pos = detail['position']
        improvement = detail['score_improvement']
        print(f"Point ({pos[0]:.2f}, {pos[1]:.2f}): {detail['chosen']} (improvement: {improvement:.2f})")
```

## Laporan Hasil

Mapping AF akan menampilkan laporan lengkap termasuk:

### 1. Grid Results (seperti biasa)
```
MAPPING AF RESULTS
==================================================
Grid Layout: 1 2 3
             4 5 6
             7 8 9

  1:(32.30,3.63)->Z=10.0834 |  2:(36.00,3.63)->Z=10.1234 |  3:(39.70,3.63)->Z=10.0987
  4:(32.30,6.17)->Z=10.1456 |  5:(36.00,6.17)->Z=10.1123 |  6:(39.70,6.17)->Z=10.0876
  7:(32.30,8.70)->Z=10.1678 |  8:(36.00,8.70)->Z=10.1345 |  9:(39.70,8.70)->Z=10.1098
```

### 2. Score Comparison Summary (baru)
```
SCORE COMPARISON SUMMARY
==================================================
  Point 1: KOREKSI (Verif:25.20 vs Koreksi:28.86)
  Point 2: VERIFIKASI (Verif:31.45 vs Koreksi:29.12)
  Point 3: KOREKSI (Verif:27.33 vs Koreksi:30.21)
  Point 4: VERIFIKASI (Verif:29.87 vs Koreksi:28.54)
  Point 5: KOREKSI (Verif:26.78 vs Koreksi:32.15)
  Point 6: VERIFIKASI (Verif:30.99 vs Koreksi:29.43)
  Point 7: KOREKSI (Verif:24.56 vs Koreksi:27.89)
  Point 8: VERIFIKASI (Verif:33.21 vs Koreksi:31.87)
  Point 9: KOREKSI (Verif:28.45 vs Koreksi:31.23)

Summary: 5 menggunakan KOREKSI, 4 menggunakan VERIFIKASI
```

## Integrasi dengan Z Interpolation

Hasil Z yang dioptimalkan dapat langsung digunakan untuk Z Interpolation:

```python
# Gunakan hasil optimized untuk interpolation
optimized_results = mapping_worker.get_optimized_results()
z_interpolation = ZInterpolation(grbl_start, grbl_end, optimized_results)
z_interpolation.create_interpolation_grid()
```

## Manfaat

1. **Akurasi Lebih Tinggi**: Memilih posisi Z dengan skor fokus terbaik
2. **Koreksi Otomatis**: Mengatasi bias sistematis dalam sapuan AF
3. **Transparansi**: Log lengkap untuk analisis dan debugging
4. **Kompatibilitas**: Tetap kompatibel dengan sistem Z Interpolation yang ada
5. **Fleksibilitas**: Dapat memilih menggunakan hasil asli atau optimized

## Catatan Teknis

- Koreksi hanya diterapkan pada mode AF normal, tidak pada refine mode
- Offset koreksi dapat disesuaikan di kode (saat ini ±0.021mm)
- Perbandingan skor menggunakan skor verifikasi, bukan skor terbaik selama sapuan
- Sistem akan fallback ke hasil asli jika terjadi error dalam proses koreksi
