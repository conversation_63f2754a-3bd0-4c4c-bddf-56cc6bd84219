# Dual Stitching Documentation

## Overview

Dual Stitching adalah fitur yang memungkinkan proses stitching ber<PERSON><PERSON> **2 kali** dengan perbedaan pada posisi Z:

1. **Run 1**: Menggunakan Z hasil interpolasi (seperti stitching normal)
2. **Run 2**: Menggunakan Z tetap yang diambil dari nilai tengah (full AF) hasil mapping AF

## Fitur Utama

### 1. Dual Z Positioning
- **Interpolated Z**: Menggunakan nilai Z yang diinterpolasi dari 3x3 mapping AF
- **Fixed Z**: Menggunakan nilai Z tetap dari center point (point 5) hasil mapping AF

### 2. Automatic Center Z Extraction
- Sistem otomatis mengekstrak nilai Z dari center point mapping AF
- Center point adalah titik yang menggunakan "full AF" dalam grid 3x3
- Koordinat center dihitung sebagai titik tengah antara grbl_start dan grbl_end

### 3. Enhanced File Naming
```
Run 1 (Interpolated Z):
stitch_r1_interp_p001_X0.000_Y0.000_Z10.1234_135024.png

Run 2 (Fixed Z):
stitch_r2_fixed_p001_X0.000_Y0.000_Z10.1543_135024.png
```

### 4. Dual Output Organization
- Folder output: `dual_stitching_YYYYMMDD_HHMMSS/`
- Info file: `stitching_info.txt` berisi detail proses
- Semua gambar dari kedua run disimpan dalam folder yang sama

## Cara Kerja

### 1. Initialization
```python
# Sistem otomatis mengekstrak center Z dari mapping results
self._extract_center_z_value()

# Setup dual stitching parameters
self.current_run = 1
self.total_runs = 2
self.center_z_value = 10.1543  # Extracted from center point
```

### 2. Grid Preparation
```python
# Prepare interpolated Z grid points (Run 1)
self.interpolated_grid_points = self._create_efficient_path(all_points)

# Prepare fixed Z grid points (Run 2)
fixed_z_points = {}
for (x, y), _ in all_points.items():
    fixed_z_points[(x, y)] = self.center_z_value
self.fixed_z_grid_points = self._create_efficient_path(fixed_z_points)
```

### 3. Dual Execution
```python
for run_number in range(1, self.total_runs + 1):
    if run_number == 1:
        self.grid_points = self.interpolated_grid_points  # Interpolated Z
    elif run_number == 2:
        self.grid_points = self.fixed_z_grid_points       # Fixed Z
    
    # Process all points in current run
    for i, (x, y, z) in enumerate(self.grid_points):
        # Move and capture with run-specific naming
```

## Keuntungan

### 1. Comparison Analysis
- Memungkinkan perbandingan hasil antara interpolated Z vs fixed Z
- Berguna untuk validasi akurasi interpolasi
- Membantu identifikasi area yang memerlukan penyesuaian Z

### 2. Backup Strategy
- Jika interpolated Z tidak optimal, fixed Z bisa menjadi fallback
- Mengurangi risiko kehilangan data karena Z positioning yang salah

### 3. Quality Control
- Dua set data untuk analisis kualitas
- Memungkinkan pemilihan hasil terbaik per area

## Output Structure

```
dual_stitching_20250825_135024/
├── stitching_info.txt                                    # Process information
├── stitch_r1_interp_p001_X0.000_Y0.000_Z10.1234_135024.png
├── stitch_r1_interp_p002_X0.500_Y0.000_Z10.1377_135024.png
├── ...
├── stitch_r2_fixed_p001_X0.000_Y0.000_Z10.1543_135024.png
├── stitch_r2_fixed_p002_X0.500_Y0.000_Z10.1543_135024.png
└── ...
```

## Logging Output

```
============================================================
STARTING RUN 1/2: STITCHING WITH INTERPOLATED Z
============================================================
[Run 1] Moving to point 1/49: X=0.000, Y=0.000, Z=10.1234
✓ Saved: stitch_r1_interp_p001_X0.000_Y0.000_Z10.1234_135024.png
...
✓ Run 1/2 completed successfully (Interpolated Z)

============================================================
STARTING RUN 2/2: STITCHING WITH FIXED Z=10.1543
============================================================
[Run 2] Moving to point 1/49: X=0.000, Y=0.000, Z=10.1543
✓ Saved: stitch_r2_fixed_p001_X0.000_Y0.000_Z10.1543_135024.png
...
✓ Run 2/2 completed successfully (Fixed Z=10.1543)

============================================================
DUAL STITCHING PROCESS COMPLETED SUCCESSFULLY
============================================================
✓ Run 1: Stitching with interpolated Z values
✓ Run 2: Stitching with fixed Z=10.1543
============================================================
```

## Error Handling

### 1. Missing Center Z
```
⚠️ Warning: Could not find valid center Z value from mapping results
⚠️ Warning: Center Z not available, will only run interpolated stitching
```
- Sistem akan fallback ke single run (interpolated Z saja)
- `total_runs` otomatis diset ke 1

### 2. Invalid Mapping Data
```
⚠️ Warning: No mapping results available for center Z extraction
```
- Sistem tetap berjalan dengan interpolated Z saja

## Integration

### Existing Code Compatibility
- Tidak memerlukan perubahan pada kode yang sudah ada
- `StitchingWorker` tetap kompatibel dengan interface lama
- Parameter `run_number` pada `_capture_and_save_image` bersifat optional

### UI Integration
- Tidak memerlukan perubahan UI
- Proses dual stitching berjalan otomatis
- Progress bar menunjukkan progress keseluruhan kedua run

## Testing

Gunakan `test_dual_stitching.py` untuk testing:

```bash
cd Software/Stitching
python test_dual_stitching.py
```

Test akan memverifikasi:
- ✓ Center Z extraction
- ✓ Dual grid preparation  
- ✓ File naming convention
- ✓ Output folder structure

## Performance Impact

- **Waktu**: ~2x lebih lama (karena 2 run)
- **Storage**: ~2x lebih banyak file
- **Memory**: Minimal impact (grid points di-reuse)
- **Movement**: Sama seperti 2x stitching normal

## Best Practices

1. **Pastikan Mapping AF berkualitas**: Center Z value bergantung pada hasil mapping AF
2. **Monitor storage space**: Dual stitching menghasilkan 2x lebih banyak file
3. **Validasi hasil**: Bandingkan kedua run untuk memilih yang terbaik
4. **Backup data**: Simpan kedua set hasil untuk analisis lebih lanjut
