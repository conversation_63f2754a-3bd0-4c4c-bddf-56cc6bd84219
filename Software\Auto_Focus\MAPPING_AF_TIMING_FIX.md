# Per<PERSON><PERSON>n Masalah Timing Refine AF di Mapping

## Ma<PERSON>ah yang <PERSON>

### 1. **Perbedaan Hasil Refine Manual vs Mapping**
- Refine manual: <PERSON>kor tinggi, hasil akurat
- Refine di mapping: <PERSON><PERSON> rendah, hasil kurang akurat
- Contoh: Manual 29 vs Mapping 26 (perbedaan signifikan)

### 2. **<PERSON><PERSON><PERSON>**

#### **A. Kecepatan Terlalu Tinggi**
```
Parameter Default (untuk manual):
- trend_scan_feedrate: 2.0 mm/min
- fine_scan_feedrate: 1.0 mm/min

Masalah: Terlalu cepat untuk sequential processing di mapping
```

#### **B. Pressure Sequential Processing**
```
Mapping AF memproses 9 titik berturut-turut:
1. Move → 2. Refine → 3. Move → 4. Refine → ... (9x)

Setiap refine "dikejar waktu" untuk menyelesaikan mapping cepat
```

#### **C. Stabilisasi Tidak Cukup**
```
- Delay antar titik: 0.5 detik (terlalu singkat)
- Tidak ada pre-stabilization sebelum refine
- Tidak ada extended delay setelah refine
```

#### **D. Parameter Early Stop Terlalu Agresif**
```
- early_stop_drop_pct: 30% (terlalu cepat berhenti)
- min_frames_before_early_stop: 15 (terlalu sedikit data)
- patience_limit: 2 (terlalu tidak sabar)
```

## Solusi yang Diterapkan

### 1. **Parameter Refine Khusus untuk Mapping**

#### **Kecepatan Diperlambat:**
```python
# Sebelum (Default)          # Sesudah (Mapping)
trend_scan_feedrate: 2.0  →  0.8 mm/min  (60% lebih lambat)
fine_scan_feedrate: 1.0   →  0.5 mm/min  (50% lebih lambat)
```

#### **Jarak Scan Diperkecil untuk Presisi:**
```python
trend_scan_distance: 0.5  →  0.3 mm  (40% lebih kecil)
fine_scan_range: 0.5      →  0.3 mm  (40% lebih kecil)
```

#### **Early Stop Lebih Konservatif:**
```python
early_stop_drop_pct: 30%     →  15%  (lebih ketat)
min_frames_before_early_stop: 15  →  20   (lebih banyak data)
min_decrease_window: 6       →  8    (window lebih besar)
patience_limit: 2            →  3    (lebih sabar)
```

#### **Stabilisasi Diperpanjang:**
```python
stabilization_sleep_ms: 80   →  150 ms  (hampir 2x lebih lama)
warmup_ms: 80                →  120 ms  (50% lebih lama)
```

### 2. **Timing Management**

#### **Pre-Stabilization:**
```python
# Sebelum refine AF dimulai:
time.sleep(1.0)           # Stabilisasi 1 detik
grbl.wait_for_idle()      # Pastikan GRBL idle
time.sleep(0.5)           # Stabilisasi tambahan
```

#### **Post-Refine Delay:**
```python
# Setelah refine AF selesai:
if af_type == 'refine':
    time.sleep(2.0)       # 2 detik untuk refine
else:
    time.sleep(0.5)       # 0.5 detik untuk full AF
```

### 3. **Automatic Parameter Management**

#### **Saat Mapping Dimulai:**
```python
def _configure_mapping_refine_parameters(self):
    # Terapkan parameter mapping yang lebih stabil
    mapping_params = {...}
    for key, value in mapping_params.items():
        config.set(key, value)
```

#### **Saat Mapping Selesai:**
```python
def _restore_default_refine_parameters(self):
    # Kembalikan ke parameter default
    default_params = {...}
    for key, value in default_params.items():
        config.set(key, value)
```

## Hasil yang Diharapkan

### 1. **Konsistensi Hasil**
- Refine di mapping seharusnya memberikan hasil serupa dengan refine manual
- Skor fokus lebih tinggi dan stabil
- Perbedaan minimal antara manual vs mapping

### 2. **Akurasi Lebih Tinggi**
- Parameter lebih konservatif menghasilkan data lebih akurat
- Lebih banyak frame untuk analisis
- Early stop lebih hati-hati

### 3. **Stabilitas Sistem**
- Stabilisasi yang cukup antar proses
- Tidak ada "rush" dalam sequential processing
- GRBL dan kamera punya waktu untuk stabilisasi

## Log Output Baru

```
Starting REFINE autofocus at (32.30, 3.63) - Using baseline Z
⚠️  MAPPING REFINE: Using slower, more stable parameters...
⏱️  Pre-stabilization for refine AF...
✓ Mapping refine parameters configured for stability:
  - Trend scan speed: 0.8 mm/min
  - Fine scan speed: 0.5 mm/min
  - Scan distances reduced for precision
  - Early stop more conservative

[AF REFINE] Tahap 1: Memulai Trend Scan ke arah bawah...
[AF REFINE] Tahap 2: Memulai Fine Scan...
✓ Refine AF finished at (32.30, 3.63): Z=10.0834, Score=28.86

⏱️  Extended stabilization delay after refine AF...

✓ Refine parameters restored to default values
```

## Perbandingan Sebelum vs Sesudah

### **Sebelum Perbaikan:**
```
Point 1: Verifikasi menang (26.5 vs 24.8) - Koreksi kalah
Point 2: Verifikasi menang (28.1 vs 26.9) - Koreksi kalah
Point 3: Verifikasi menang (27.3 vs 25.1) - Koreksi kalah
...
Hasil: Mayoritas verifikasi menang (tidak sesuai dengan manual)
```

### **Sesudah Perbaikan (Diharapkan):**
```
Point 1: Koreksi menang (28.8 vs 25.2) - Sesuai dengan manual
Point 2: Koreksi menang (31.2 vs 28.4) - Sesuai dengan manual  
Point 3: Koreksi menang (29.7 vs 27.1) - Sesuai dengan manual
...
Hasil: Mayoritas koreksi menang (konsisten dengan manual)
```

## Testing dan Validasi

### 1. **Test Konsistensi:**
- Jalankan mapping AF dengan parameter baru
- Bandingkan hasil dengan refine manual di titik yang sama
- Pastikan perbedaan skor minimal (< 2 poin)

### 2. **Test Stabilitas:**
- Jalankan mapping beberapa kali di area yang sama
- Pastikan hasil konsisten antar run
- Variasi Z < 0.01mm antar run

### 3. **Test Performance:**
- Ukur waktu total mapping (akan lebih lama tapi lebih akurat)
- Pastikan tidak ada timeout atau error
- Monitor stabilitas GRBL dan kamera

## Catatan Implementasi

1. **Backward Compatibility:** Parameter default tetap sama untuk refine manual
2. **Automatic Management:** Parameter diubah otomatis hanya untuk mapping
3. **Error Handling:** Fallback ke default jika terjadi error
4. **Logging:** Log lengkap untuk debugging dan monitoring
5. **Configurable:** Parameter dapat disesuaikan di masa depan jika diperlukan
